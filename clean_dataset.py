#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集清理脚本
删除没有对应标注文件的图片，确保图片和标注文件一一对应
"""

import os
import shutil
from pathlib import Path

def clean_dataset(image_dir, annotation_dir):
    """
    清理数据集，删除没有对应标注文件的图片
    
    Args:
        image_dir: 图片文件夹路径
        annotation_dir: 标注文件夹路径
    """
    
    print("开始清理数据集...")
    
    # 获取所有图片文件
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = []
    
    for file in os.listdir(image_dir):
        file_path = os.path.join(image_dir, file)
        if os.path.isfile(file_path):
            _, ext = os.path.splitext(file)
            if ext.lower() in image_extensions:
                image_files.append(file)
    
    # 获取所有标注文件
    annotation_files = []
    for file in os.listdir(annotation_dir):
        if file.endswith('.txt') and file != 'classes.txt':  # 排除classes.txt
            annotation_files.append(file)
    
    print(f"找到图片文件: {len(image_files)} 个")
    print(f"找到标注文件: {len(annotation_files)} 个")
    
    # 创建标注文件名集合（不含扩展名）
    annotation_basenames = set()
    for ann_file in annotation_files:
        basename = os.path.splitext(ann_file)[0]
        annotation_basenames.add(basename)
    
    # 检查每个图片是否有对应的标注文件
    images_to_delete = []
    images_with_annotations = []
    
    for img_file in image_files:
        img_basename = os.path.splitext(img_file)[0]
        
        if img_basename in annotation_basenames:
            images_with_annotations.append(img_file)
        else:
            images_to_delete.append(img_file)
    
    print(f"\n统计结果:")
    print(f"有对应标注的图片: {len(images_with_annotations)} 个")
    print(f"没有对应标注的图片: {len(images_to_delete)} 个")
    
    if len(images_to_delete) == 0:
        print("✅ 所有图片都有对应的标注文件，无需删除！")
        return
    
    print(f"\n将要删除的图片文件:")
    for i, img_file in enumerate(images_to_delete[:10], 1):  # 只显示前10个
        print(f"  {i}. {img_file}")
    
    if len(images_to_delete) > 10:
        print(f"  ... 还有 {len(images_to_delete) - 10} 个文件")
    
    # 询问用户确认
    while True:
        user_input = input(f"\n确认删除这 {len(images_to_delete)} 个没有标注的图片文件吗? (y/n): ").lower().strip()
        if user_input in ['y', 'yes', '是']:
            break
        elif user_input in ['n', 'no', '否']:
            print("操作已取消")
            return
        else:
            print("请输入 y 或 n")
    
    # 创建备份文件夹（可选）
    backup_dir = "deleted_images_backup"
    create_backup = False
    
    backup_input = input("是否创建备份文件夹保存被删除的图片? (y/n): ").lower().strip()
    if backup_input in ['y', 'yes', '是']:
        create_backup = True
        os.makedirs(backup_dir, exist_ok=True)
        print(f"备份文件夹已创建: {backup_dir}")
    
    # 删除图片文件
    deleted_count = 0
    failed_count = 0
    
    for img_file in images_to_delete:
        img_path = os.path.join(image_dir, img_file)
        
        try:
            if create_backup:
                # 移动到备份文件夹
                backup_path = os.path.join(backup_dir, img_file)
                shutil.move(img_path, backup_path)
                print(f"已备份: {img_file}")
            else:
                # 直接删除
                os.remove(img_path)
                print(f"已删除: {img_file}")
            
            deleted_count += 1
            
        except Exception as e:
            print(f"删除失败 {img_file}: {e}")
            failed_count += 1
    
    print(f"\n清理完成!")
    print(f"成功处理: {deleted_count} 个文件")
    print(f"失败: {failed_count} 个文件")
    print(f"剩余图片: {len(images_with_annotations)} 个")
    
    if create_backup:
        print(f"备份位置: {backup_dir}")

def check_annotation_orphans(image_dir, annotation_dir):
    """
    检查是否有孤立的标注文件（没有对应图片）
    """
    print("\n检查孤立的标注文件...")
    
    # 获取所有图片文件名（不含扩展名）
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_basenames = set()
    
    for file in os.listdir(image_dir):
        file_path = os.path.join(image_dir, file)
        if os.path.isfile(file_path):
            _, ext = os.path.splitext(file)
            if ext.lower() in image_extensions:
                basename = os.path.splitext(file)[0]
                image_basenames.add(basename)
    
    # 检查标注文件
    orphan_annotations = []
    for file in os.listdir(annotation_dir):
        if file.endswith('.txt') and file != 'classes.txt':
            basename = os.path.splitext(file)[0]
            if basename not in image_basenames:
                orphan_annotations.append(file)
    
    if orphan_annotations:
        print(f"发现 {len(orphan_annotations)} 个孤立的标注文件:")
        for ann_file in orphan_annotations[:10]:
            print(f"  - {ann_file}")
        if len(orphan_annotations) > 10:
            print(f"  ... 还有 {len(orphan_annotations) - 10} 个文件")
    else:
        print("✅ 没有发现孤立的标注文件")

def main():
    """
    主函数
    """
    image_dir = "tupian"        # 图片文件夹
    annotation_dir = "yolo"     # YOLO标注文件夹
    
    # 检查目录是否存在
    if not os.path.exists(image_dir):
        print(f"错误: 图片目录 '{image_dir}' 不存在!")
        return
    
    if not os.path.exists(annotation_dir):
        print(f"错误: 标注目录 '{annotation_dir}' 不存在!")
        return
    
    print("="*60)
    print("数据集清理工具")
    print("="*60)
    
    # 清理没有标注的图片
    clean_dataset(image_dir, annotation_dir)
    
    # 检查孤立的标注文件
    check_annotation_orphans(image_dir, annotation_dir)
    
    print("\n" + "="*60)
    print("清理完成!")

if __name__ == "__main__":
    main()
